/**
 * Viva Wallet Payment Service
 * 
 * Frontend service for handling Viva Wallet payment integration
 * Supports payment creation and payment status tracking
 */

import { apiService } from './api';

export interface VivaPaymentRequest {
  orderId: string;
  amount: number; // Amount in cents
  currency?: string;
  customerTrns?: string;
  payerName: string;
  payerEmail?: string;
  successUrl: string;
  cancelUrl: string;
  sourceCode?: string;
  type?: 'url'; // Viva only supports payment URLs
}

export interface VivaPaymentResponse {
  transaction_id: string;
  order_id: string;
  orderCode: number;
  redirect_url: string;
  amount: number;
  currency: string;
  type: string;
  status: 'pending' | 'completed' | 'failed' | 'expired' | 'cancelled';
}

export interface VivaPaymentStatusResponse {
  orderCode: number;
  status: 'pending' | 'completed' | 'failed' | 'expired' | 'cancelled';
  amount: number;
  currency: string;
  stateId: number;
  expirationDate: string;
  merchantTrns?: string;
  customerTrns?: string;
}

export interface VivaConfigResponse {
  environment: 'demo' | 'production';
  enabled: boolean;
  currency: string;
  sourceCode: string;
  checkoutUrl: string;
  minAmount: number;
  maxAmount: number;
}

export interface VivaHealthResponse {
  status: string;
  environment: string;
  timestamp: string;
}

export interface VivaServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any;
}

class VivaPaymentService {
  private baseUrl = '/viva';

  /**
   * Create a new Viva Wallet payment
   */
  async createPayment(params: VivaPaymentRequest): Promise<VivaServiceResponse<VivaPaymentResponse>> {
    try {
      console.log('Creating Viva Wallet payment with params:', params);
      
      const response = await apiService.post(`${this.baseUrl}/payment`, params);
      
      console.log('Viva Wallet payment response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Viva Wallet payment creation failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to connect to Viva Wallet payment service',
      };
    }
  }

  /**
   * Get payment status by order code
   */
  async getPaymentStatus(orderCode: number): Promise<VivaServiceResponse<VivaPaymentStatusResponse>> {
    try {
      console.log('Getting Viva Wallet payment status for order code:', orderCode);
      
      const response = await apiService.get(`${this.baseUrl}/payment/${orderCode}/status`);
      
      console.log('Viva Wallet payment status response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Viva Wallet payment status retrieval failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to get payment status',
      };
    }
  }

  /**
   * Cancel a payment by order code
   */
  async cancelPayment(orderCode: number): Promise<VivaServiceResponse<{ orderCode: number; cancelled: boolean }>> {
    try {
      console.log('Cancelling Viva Wallet payment for order code:', orderCode);
      
      const response = await apiService.delete(`${this.baseUrl}/payment/${orderCode}`);
      
      console.log('Viva Wallet payment cancellation response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Viva Wallet payment cancellation failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to cancel payment',
      };
    }
  }

  /**
   * Get Viva Wallet configuration
   */
  async getConfig(): Promise<VivaServiceResponse<VivaConfigResponse>> {
    try {
      console.log('Getting Viva Wallet configuration');
      
      const response = await apiService.get(`${this.baseUrl}/config`);
      
      console.log('Viva Wallet config response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Viva Wallet config retrieval failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to get configuration',
      };
    }
  }

  /**
   * Health check for Viva Wallet service
   */
  async healthCheck(): Promise<VivaServiceResponse<VivaHealthResponse>> {
    try {
      console.log('Performing Viva Wallet health check');
      
      const response = await apiService.get(`${this.baseUrl}/health`);
      
      console.log('Viva Wallet health check response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Viva Wallet health check failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Service unavailable',
      };
    }
  }

  /**
   * Generate order ID for payments
   */
  generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `VIVA_${timestamp}_${random}`;
  }

  /**
   * Format amount for display (convert from pence to currency units)
   */
  formatAmount(amount: number, currency: string = 'GBP'): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  }

  /**
   * Check if payment is expired
   */
  isPaymentExpired(expirationDate: string): boolean {
    return new Date(expirationDate) < new Date();
  }

  /**
   * Get time remaining until payment expires
   */
  getTimeRemaining(expirationDate: string): number {
    const expiry = new Date(expirationDate);
    const now = new Date();
    return Math.max(0, expiry.getTime() - now.getTime());
  }

  /**
   * Format time remaining for display
   */
  formatTimeRemaining(expirationDate: string): string {
    const remaining = this.getTimeRemaining(expirationDate);
    
    if (remaining <= 0) {
      return 'Expired';
    }

    const minutes = Math.floor(remaining / (1000 * 60));
    const seconds = Math.floor((remaining % (1000 * 60)) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    
    return `${seconds}s`;
  }

  /**
   * Get success URL for payment completion
   */
  getSuccessUrl(): string {
    return `${window.location.origin}/payment/success`;
  }

  /**
   * Get cancel URL for payment cancellation
   */
  getCancelUrl(): string {
    return `${window.location.origin}/payment/cancel`;
  }

  /**
   * Generate payment URL for Viva Wallet checkout
   */
  generatePaymentUrl(orderCode: number, checkoutUrl: string): string {
    return `${checkoutUrl}/web/checkout?ref=${orderCode}`;
  }

  /**
   * Map Viva Wallet StateId to status text
   */
  mapStateIdToStatusText(stateId: number): string {
    switch (stateId) {
      case 0:
        return 'Pending';
      case 1:
        return 'Expired';
      case 2:
        return 'Cancelled';
      case 3:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get status color for UI display
   */
  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600';
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'expired':
        return 'text-gray-600';
      case 'cancelled':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  }
}

export const vivaPaymentService = new VivaPaymentService();
