"use strict";
/**
 * Viva Wallet Payment Service
 *
 * Service for handling Viva Wallet payment integration
 * Supports OAuth2 authentication, payment order creation, and payment status tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.vivaPaymentService = void 0;
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const vivaLogger = logger_1.logger.child({ module: 'viva-payment-service' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int().min(30), // Minimum 30 pence as per Viva requirements
    currency: zod_1.z.string().length(3).default('GBP'),
    customerTrns: zod_1.z.string().optional(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    sourceCode: zod_1.z.string().optional(),
});
const paymentOrderResponseSchema = zod_1.z.object({
    OrderCode: zod_1.z.number(),
    ErrorCode: zod_1.z.number().optional(),
    ErrorText: zod_1.z.string().nullable().optional(),
    TimeStamp: zod_1.z.string().optional(),
    CorrelationId: zod_1.z.string().nullable().optional(),
    EventId: zod_1.z.number().optional(),
    Success: zod_1.z.boolean().optional(),
});
const paymentDetailsResponseSchema = zod_1.z.object({
    OrderCode: zod_1.z.number(),
    SourceCode: zod_1.z.string(),
    Tags: zod_1.z.array(zod_1.z.string()),
    TipAmount: zod_1.z.number(),
    RequestLang: zod_1.z.string(),
    MerchantTrns: zod_1.z.string().nullable(),
    CustomerTrns: zod_1.z.string().nullable(),
    MaxInstallments: zod_1.z.number(),
    RequestAmount: zod_1.z.number(),
    ExpirationDate: zod_1.z.string(),
    StateId: zod_1.z.number(), // 0=Pending, 1=Expired, 2=Canceled, 3=Paid
});
const oauthTokenResponseSchema = zod_1.z.object({
    access_token: zod_1.z.string(),
    token_type: zod_1.z.string(),
    expires_in: zod_1.z.number(),
    scope: zod_1.z.string().optional(),
});
class VivaPaymentService {
    constructor() {
        this.accessToken = null;
        this.merchantId = env_1.env.VIVA_MERCHANT_ID;
        this.apiKey = env_1.env.VIVA_API_KEY;
        this.clientId = env_1.env.VIVA_CLIENT_ID;
        this.clientSecret = env_1.env.VIVA_CLIENT_SECRET;
        this.sourceCode = env_1.env.VIVA_SOURCE_CODE;
        this.environment = env_1.env.VIVA_ENVIRONMENT;
        // Set URLs based on environment as per Viva documentation
        if (this.environment === 'production') {
            this.apiUrl = 'https://api.vivapayments.com';
            this.accountsUrl = 'https://accounts.vivapayments.com';
            this.checkoutUrl = 'https://www.vivapayments.com';
        }
        else {
            this.apiUrl = 'https://demo-api.vivapayments.com';
            this.accountsUrl = 'https://demo-accounts.vivapayments.com';
            this.checkoutUrl = 'https://demo.vivapayments.com';
        }
        // Initialize API client for payment operations
        this.client = axios_1.default.create({
            baseURL: this.apiUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });
        // Initialize accounts client for OAuth operations
        this.accountsClient = axios_1.default.create({
            baseURL: this.accountsUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json',
            },
        });
        // Add request interceptors for logging
        this.client.interceptors.request.use((config) => {
            vivaLogger.debug('Viva API request', {
                method: config.method?.toUpperCase(),
                url: config.url,
                headers: config.headers,
            });
            return config;
        }, (error) => {
            vivaLogger.error('Viva API request error', error);
            return Promise.reject(error);
        });
        this.client.interceptors.response.use((response) => {
            vivaLogger.debug('Viva API response', {
                status: response.status,
                data: response.data,
            });
            return response;
        }, (error) => {
            vivaLogger.error('Viva API response error', {
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Get OAuth2 access token for API authentication using client credentials flow
     */
    async getAccessToken() {
        try {
            // Check if we have a valid token
            if (this.accessToken && this.accessToken.expires_at > Date.now()) {
                return this.accessToken.access_token;
            }
            vivaLogger.info('Requesting new OAuth2 access token', {
                clientId: this.clientId,
                environment: this.environment,
                accountsUrl: this.accountsUrl,
            });
            // Create Base64 encoded credentials as per Viva documentation
            const credentials = Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64');
            const response = await this.accountsClient.post('/connect/token', new URLSearchParams({
                grant_type: 'client_credentials',
            }).toString(), {
                headers: {
                    'Authorization': `Basic ${credentials}`,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });
            const tokenData = oauthTokenResponseSchema.parse(response.data);
            // Store token with calculated expiration (tokens last 3600 seconds = 1 hour)
            this.accessToken = {
                access_token: tokenData.access_token,
                token_type: tokenData.token_type,
                expires_in: tokenData.expires_in,
                expires_at: Date.now() + (tokenData.expires_in * 1000) - 60000, // Subtract 1 minute for safety
                scope: tokenData.scope,
            };
            vivaLogger.info('OAuth2 access token obtained successfully', {
                expires_in: tokenData.expires_in,
                token_type: tokenData.token_type,
                scope: tokenData.scope,
            });
            return this.accessToken.access_token;
        }
        catch (error) {
            vivaLogger.error('Failed to get OAuth2 access token', {
                error: error.message,
                response: error.response?.data,
                status: error.response?.status,
                clientId: this.clientId,
                environment: this.environment,
            });
            // Provide specific error messages based on the response
            if (error.response?.data?.error === 'invalid_client') {
                const detailedError = `
OAuth2 Authentication Failed - Invalid Client Credentials

Current Credentials:
- Client ID: ${this.clientId}
- Environment: ${this.environment}

Troubleshooting Steps:
1. Log into Viva dashboard: https://demo.vivapayments.com/
2. Go to: Settings > API Access > Smart Checkout Credentials
3. Verify Client ID matches exactly: ${this.clientId}
4. Regenerate Client Secret if needed
5. Ensure Smart Checkout is enabled for your account
6. Contact Viva support if the issue persists

Note: Basic Auth is working correctly for payment status/cancellation operations.
        `.trim();
                throw new Error(detailedError);
            }
            throw new Error(`Failed to authenticate with Viva Wallet: ${error.message}`);
        }
    }
    /**
     * Get Basic Auth credentials for API authentication
     */
    getBasicAuthHeader() {
        const credentials = Buffer.from(`${this.merchantId}:${this.apiKey}`).toString('base64');
        return `Basic ${credentials}`;
    }
    /**
     * Create a new Viva Wallet payment order
     *
     * Note: Viva Wallet requires OAuth2 for payment creation.
     * Basic Auth only works for payment status, cancellation, and refunds.
     */
    async createPayment(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            vivaLogger.info('Creating Viva Wallet payment order', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            // Viva Wallet requires OAuth2 for payment creation
            // Basic Auth is only supported for payment status/cancellation
            return await this.createPaymentWithOAuth2(validatedParams);
        }
        catch (error) {
            vivaLogger.error('Viva Wallet payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: error.errors,
                };
            }
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Failed to create payment',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Create payment using OAuth2 (following working JavaScript implementation)
     */
    async createPaymentWithOAuth2(params) {
        try {
            vivaLogger.info('Attempting payment creation with OAuth2');
            // Get OAuth2 access token
            const accessToken = await this.getAccessToken();
            // Use the exact same payload structure that works in the JavaScript test
            const requestData = {
                amount: params.amount,
                customerTrns: params.customerTrns || `Payment for order ${params.orderId}`,
                customer: {
                    email: params.payerEmail || '<EMAIL>',
                    fullName: params.payerName || 'Customer',
                    requestLang: 'en-GB',
                },
                sourceCode: params.sourceCode || this.sourceCode || 'Default',
                merchantTrns: params.orderId,
                currencyCode: this.getCurrencyCode(params.currency), // This should return '978' for EUR
                paymentTimeout: 1800,
            };
            vivaLogger.info('Viva payment request data', {
                amount: requestData.amount,
                currency: params.currency,
                currencyCode: requestData.currencyCode,
                merchantTrns: requestData.merchantTrns,
                fullRequestData: requestData,
                originalParams: params,
            });
            const response = await this.client.post('/checkout/v2/orders', requestData, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            });
            vivaLogger.info('Viva payment response received', {
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                data: response.data,
                dataType: typeof response.data,
                dataKeys: response.data ? Object.keys(response.data) : [],
                rawResponseData: JSON.stringify(response.data),
            });
            console.log('=== RAW VIVA API RESPONSE ===');
            console.log('Status:', response.status);
            console.log('Data:', JSON.stringify(response.data, null, 2));
            console.log('Data Type:', typeof response.data);
            console.log('Data Keys:', response.data ? Object.keys(response.data) : []);
            console.log('Data Values:', response.data ? Object.values(response.data) : []);
            console.log('=== END RAW RESPONSE ===');
            // Handle the response format from the working JavaScript test
            // The response might be just { "orderCode": 9030155980390464 }
            let orderCode;
            try {
                vivaLogger.info('Processing Viva response for order code', {
                    responseData: response.data,
                    dataType: typeof response.data,
                    isObject: typeof response.data === 'object',
                    keys: response.data ? Object.keys(response.data) : [],
                    values: response.data ? Object.values(response.data) : [],
                });
                if (response.data && typeof response.data === 'object') {
                    // Check for different possible response formats
                    orderCode = response.data.orderCode || response.data.OrderCode || response.data.order_code;
                    vivaLogger.info('Initial order code extraction', {
                        orderCode,
                        foundDirectly: !!orderCode,
                    });
                    if (!orderCode && typeof response.data === 'object' && Object.keys(response.data).length === 1) {
                        // If there's only one property, it might be the order code
                        const firstKey = Object.keys(response.data)[0];
                        const firstValue = Object.values(response.data)[0];
                        vivaLogger.info('Trying single property extraction', {
                            firstKey,
                            firstValue,
                            firstValueType: typeof firstValue,
                        });
                        if (typeof firstValue === 'string' || typeof firstValue === 'number') {
                            orderCode = firstValue;
                        }
                    }
                    // Additional fallback: check if the response data itself is the order code
                    if (!orderCode && (typeof response.data === 'string' || typeof response.data === 'number')) {
                        orderCode = response.data;
                        vivaLogger.info('Using response data directly as order code', { orderCode });
                    }
                }
                vivaLogger.info('Final order code extraction result', {
                    orderCode,
                    orderCodeType: typeof orderCode,
                    success: !!orderCode,
                });
                if (!orderCode) {
                    vivaLogger.error('No order code found in response', {
                        responseData: response.data,
                        responseStatus: response.status,
                        responseHeaders: response.headers,
                    });
                    return {
                        success: false,
                        error: 'INVALID_RESPONSE',
                        message: 'No order code received from Viva Wallet',
                        details: {
                            responseData: response.data,
                            responseStatus: response.status,
                        },
                    };
                }
            }
            catch (extractionError) {
                vivaLogger.error('Error extracting order code from response', {
                    error: extractionError.message,
                    responseData: response.data,
                });
                return {
                    success: false,
                    error: 'RESPONSE_PROCESSING_ERROR',
                    message: 'Failed to process Viva Wallet response',
                    details: {
                        extractionError: extractionError.message,
                        responseData: response.data,
                    },
                };
            }
            try {
                const checkoutUrl = `${this.checkoutUrl}/web/checkout?ref=${orderCode}`;
                vivaLogger.info('Creating payment response object', {
                    orderCode,
                    orderCodeType: typeof orderCode,
                    checkoutUrl,
                    amount: params.amount,
                    currency: params.currency,
                });
                const paymentResponse = {
                    orderCode: Number(orderCode),
                    checkout_url: checkoutUrl,
                    payment_url: checkoutUrl,
                    amount: params.amount,
                    currency: params.currency,
                    status: 'pending',
                };
                vivaLogger.info('Viva Wallet payment order created successfully with OAuth2', {
                    orderCode: orderCode,
                    orderCodeNumber: Number(orderCode),
                    orderId: params.orderId,
                    checkoutUrl,
                    paymentResponse,
                });
                return {
                    success: true,
                    data: paymentResponse,
                };
            }
            catch (responseCreationError) {
                vivaLogger.error('Error creating payment response object', {
                    error: responseCreationError.message,
                    orderCode,
                    orderCodeType: typeof orderCode,
                });
                return {
                    success: false,
                    error: 'RESPONSE_CREATION_ERROR',
                    message: 'Failed to create payment response',
                    details: {
                        error: responseCreationError.message,
                        orderCode,
                    },
                };
            }
        }
        catch (error) {
            vivaLogger.error('OAuth2 payment creation failed', {
                error: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                url: error.config?.url,
                method: error.config?.method,
            });
            // Provide more specific error messages
            let errorMessage = error.message;
            if (error.response?.status === 403) {
                errorMessage = 'Currency not supported or account configuration issue';
            }
            else if (error.response?.status === 401) {
                errorMessage = 'OAuth2 authentication failed';
            }
            else if (error.response?.status === 400) {
                errorMessage = 'Invalid payment request data';
            }
            return {
                success: false,
                error: 'OAUTH2_ERROR',
                message: errorMessage,
                details: {
                    status: error.response?.status,
                    data: error.response?.data,
                    originalError: error.message,
                },
            };
        }
    }
    /**
     * Get payment order details and status
     */
    async getPaymentStatus(orderCode) {
        try {
            vivaLogger.info('Getting Viva Wallet payment status', { orderCode });
            // Use the correct URL structure for getting order details
            // Demo: https://demo.vivapayments.com/api/orders/{orderCode}
            // Production: https://www.vivapayments.com/api/orders/{orderCode}
            const statusUrl = this.environment === 'production'
                ? `https://www.vivapayments.com/api/orders/${orderCode}`
                : `https://demo.vivapayments.com/api/orders/${orderCode}`;
            const response = await axios_1.default.get(statusUrl, {
                headers: {
                    'Authorization': this.getBasicAuthHeader(),
                },
            });
            const orderDetails = paymentDetailsResponseSchema.parse(response.data);
            const status = this.mapStateIdToStatus(orderDetails.StateId);
            const statusResponse = {
                orderCode: orderDetails.OrderCode,
                status,
                amount: orderDetails.RequestAmount,
                currency: 'EUR', // Default currency, could be enhanced to detect from order
                stateId: orderDetails.StateId,
                expirationDate: orderDetails.ExpirationDate,
                merchantTrns: orderDetails.MerchantTrns,
                customerTrns: orderDetails.CustomerTrns,
            };
            vivaLogger.info('Viva Wallet payment status retrieved successfully', {
                orderCode,
                status,
                stateId: orderDetails.StateId,
            });
            return {
                success: true,
                data: statusResponse,
            };
        }
        catch (error) {
            vivaLogger.error('Failed to get Viva Wallet payment status', { orderCode, error });
            return {
                success: false,
                error: 'PAYMENT_STATUS_RETRIEVAL_FAILED',
                message: 'Failed to get payment status',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Cancel a payment order
     */
    async cancelPayment(orderCode) {
        try {
            vivaLogger.info('Cancelling Viva Wallet payment order', { orderCode });
            // Use the correct URL structure for cancelling orders
            const cancelUrl = this.environment === 'production'
                ? `https://www.vivapayments.com/api/orders/${orderCode}`
                : `https://demo.vivapayments.com/api/orders/${orderCode}`;
            const response = await axios_1.default.delete(cancelUrl, {
                headers: {
                    'Authorization': this.getBasicAuthHeader(),
                },
            });
            const cancelResponse = paymentOrderResponseSchema.parse(response.data);
            if (cancelResponse.ErrorCode && cancelResponse.ErrorCode !== 0) {
                vivaLogger.error('Viva Wallet payment cancellation failed', {
                    orderCode,
                    errorCode: cancelResponse.ErrorCode,
                    errorText: cancelResponse.ErrorText,
                });
                return {
                    success: false,
                    error: 'PAYMENT_CANCELLATION_FAILED',
                    message: cancelResponse.ErrorText || 'Failed to cancel payment',
                    details: cancelResponse,
                };
            }
            vivaLogger.info('Viva Wallet payment order cancelled successfully', { orderCode });
            return {
                success: true,
                data: {
                    orderCode,
                    cancelled: true,
                },
            };
        }
        catch (error) {
            vivaLogger.error('Failed to cancel Viva Wallet payment', { orderCode, error });
            return {
                success: false,
                error: 'PAYMENT_CANCELLATION_FAILED',
                message: 'Failed to cancel payment',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Health check for Viva Wallet service
     */
    async healthCheck() {
        try {
            vivaLogger.info('Performing Viva Wallet health check');
            // Try to get an access token to verify authentication
            await this.getAccessToken();
            vivaLogger.info('Viva Wallet health check passed');
            return {
                success: true,
                data: {
                    status: 'healthy',
                    environment: this.environment,
                },
            };
        }
        catch (error) {
            vivaLogger.error('Viva Wallet health check failed', error);
            return {
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Viva Wallet service is not available',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Map Viva Wallet StateId to our standard status
     */
    mapStateIdToStatus(stateId) {
        switch (stateId) {
            case 0:
                return 'pending';
            case 1:
                return 'expired';
            case 2:
                return 'cancelled';
            case 3:
                return 'completed';
            default:
                return 'failed';
        }
    }
    /**
     * Get numeric currency code for Viva Wallet API
     */
    getCurrencyCode(currency) {
        const currencyCodes = {
            'EUR': '978',
            'USD': '840',
            'GBP': '826',
            'CHF': '756',
            'SEK': '752',
            'NOK': '578',
            'DKK': '208',
            'PLN': '985',
            'CZK': '203',
            'HUF': '348',
            'RON': '946',
            'BGN': '975',
            'HRK': '191',
        };
        return currencyCodes[currency.toUpperCase()] || '978'; // Default to EUR
    }
    /**
     * Generate order ID for payments
     */
    generateOrderId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `VIVA_${timestamp}_${random}`;
    }
    /**
     * Format amount for display (convert from cents to currency units)
     */
    formatAmount(amount, currency = 'EUR') {
        return new Intl.NumberFormat('en-GB', {
            style: 'currency',
            currency: currency,
        }).format(amount / 100);
    }
    /**
     * Check if payment is expired
     */
    isPaymentExpired(expirationDate) {
        return new Date(expirationDate) < new Date();
    }
    /**
     * Get time remaining until payment expires
     */
    getTimeRemaining(expirationDate) {
        const expiry = new Date(expirationDate);
        const now = new Date();
        return Math.max(0, expiry.getTime() - now.getTime());
    }
    /**
     * Format time remaining for display
     */
    formatTimeRemaining(expirationDate) {
        const remaining = this.getTimeRemaining(expirationDate);
        if (remaining <= 0) {
            return 'Expired';
        }
        const minutes = Math.floor(remaining / (1000 * 60));
        const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
        if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        }
        return `${seconds}s`;
    }
    /**
     * Get success URL for payment completion
     */
    getSuccessUrl() {
        return `${env_1.env.BASE_URL}/payment/success`;
    }
    /**
     * Get cancel URL for payment cancellation
     */
    getCancelUrl() {
        return `${env_1.env.BASE_URL}/payment/cancel`;
    }
}
exports.vivaPaymentService = new VivaPaymentService();
//# sourceMappingURL=vivaPaymentService.js.map