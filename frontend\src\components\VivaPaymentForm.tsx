/**
 * Viva Wallet Payment Form Component
 *
 * Provides Viva Wallet payment integration for payment processing
 * Maintains POS terminal UI design with mobile responsiveness
 */

import { useState } from 'react';
import { toast } from 'sonner';
import { ExternalLink, Copy } from 'lucide-react';
import { useViva, useVivaConfig } from '../hooks/useViva';
import { AndroidNumericKeypad } from './AndroidNumericKeypad';

interface VivaPaymentFormProps {
  amount?: number; // Amount in cents (optional, defaults to 0)
  currency?: string;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  customerData?: {
    firstName?: string;
    lastName?: string;
    email?: string;
  };
  className?: string;
}

export function VivaPaymentForm({
  currency = 'EUR',
  onSuccess = () => {},
  onError = () => {},
  onCancel,
  customerData,
  className = ''
}: VivaPaymentFormProps) {
  const [amount, setAmount] = useState(0); // Always start with 0
  const [amountInput, setAmountInput] = useState(''); // Always start empty
  const [showOptions, setShowOptions] = useState(false);
  const [payerName, setPayerName] = useState(
    customerData?.firstName && customerData?.lastName
      ? `${customerData.firstName} ${customerData.lastName}`
      : ''
  );
  const [payerEmail, setPayerEmail] = useState(customerData?.email || '');
  const [customerTrns, setCustomerTrns] = useState('');

  const { 
    createPayment, 
    isLoading, 
    generateOrderId, 
    getSuccessUrl, 
    getCancelUrl,
    formatAmount 
  } = useViva();
  
  const { data: vivaConfig } = useVivaConfig();

  // Get currency from backend configuration (GBP for Viva Wallet)
  const selectedCurrency = currency; // Use the passed currency (GBP)

  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    
    // Convert to cents for internal use
    const numericValue = parseFloat(value) || 0;
    const amountInCents = Math.round(numericValue * 100);
    setAmount(amountInCents);
  };

  const handleKeypadChange = (newValue: string) => {
    setAmountInput(newValue);
    const numericValue = parseFloat(newValue) || 0;
    setAmount(Math.round(numericValue * 100));
  };

  const handleMoreOptions = () => {
    setShowOptions(true);
  };

  const handleCreatePayment = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount < 30) { // Viva Wallet minimum amount is 30 cents
      toast.error('Minimum amount is €0.30');
      return;
    }

    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        customerTrns: customerTrns.trim() || `Payment for order ${orderId}`,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      if (result.success && result.data) {
        // Call onSuccess callback BEFORE redirecting to prevent error popup
        onSuccess(result.data);

        // Open payment URL in the same tab (like Square)
        window.location.href = result.data.redirect_url;
        toast.success('Redirecting to payment page...');
      } else {
        toast.error(result.message || 'Failed to create payment');
        onError(result);
      }
    } catch (error) {
      console.error('Payment creation error:', error);
      toast.error('Failed to create payment');
      onError(error);
    }
  };

  const handlePaymentWithOptions = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount < 30) {
      toast.error('Minimum amount is €0.30');
      return;
    }

    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        customerTrns: customerTrns.trim() || `Payment for order ${orderId}`,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      if (result.success && result.data) {
        // Show confirmation popup before opening URL (like Vivid does)
        const userConfirmed = window.confirm(
          'Payment URL generated successfully!\n\n' +
          'Click "OK" to open the payment page in a new tab, or "Cancel" to copy the URL to clipboard.'
        );

        if (userConfirmed) {
          // Try to open in new tab
          const newWindow = window.open(result.data.redirect_url, '_blank', 'noopener,noreferrer');

          // Check if popup was blocked
          if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            // Popup was blocked, copy to clipboard instead
            await navigator.clipboard.writeText(result.data.redirect_url);
            toast.success('Popup blocked! Payment URL copied to clipboard. Please paste it in a new tab.');
          } else {
            toast.success('Payment page opened in new tab');
          }
        } else {
          // User chose to copy URL
          await navigator.clipboard.writeText(result.data.redirect_url);
          toast.success('Payment URL copied to clipboard!');
        }

        // Call onSuccess callback and reset options
        onSuccess(result.data);
        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate payment URL');
        onError(result);
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
      onError(error);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 font-['Poppins']">
          Viva Wallet Payment
        </h2>
        <p className="text-gray-600 mt-2 font-['Poppins']">
          Secure payment processing
        </p>
      </div>

      <div className="space-y-4">
        {/* Amount Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 font-['Poppins']">
            Amount ({selectedCurrency})
          </label>
          <input
            type="text"
            value={amountInput}
            onChange={(e) => handleAmountChange(e.target.value)}
            placeholder="0.00"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg font-mono"
          />
          {amount > 0 && (
            <p className="text-sm text-gray-600 mt-1 font-['Poppins']">
              {formatAmount(amount, selectedCurrency)}
            </p>
          )}
        </div>

        {/* Numeric Keypad */}
        <AndroidNumericKeypad
          value={amountInput}
          onChange={handleKeypadChange}
          currency={selectedCurrency}
          placeholder="0.00"
          allowDecimal={true}
        />

        {/* Payer Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 font-['Poppins']">
            Payer Name *
          </label>
          <input
            type="text"
            value={payerName}
            onChange={(e) => setPayerName(e.target.value)}
            placeholder="Enter payer name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
            required
          />
        </div>

        {/* Payer Email (Optional) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 font-['Poppins']">
            Payer Email (Optional)
          </label>
          <input
            type="email"
            value={payerEmail}
            onChange={(e) => setPayerEmail(e.target.value)}
            placeholder="Enter email address"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
          />
        </div>

        {/* Customer Transaction Description (Optional) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 font-['Poppins']">
            Payment Description (Optional)
          </label>
          <input
            type="text"
            value={customerTrns}
            onChange={(e) => setCustomerTrns(e.target.value)}
            placeholder="Enter payment description"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Poppins']"
          />
        </div>

        {/* Action Buttons */}
        {!showOptions ? (
          <div className="grid grid-cols-1 gap-3 pt-4">
            <button
              onClick={handleCreatePayment}
              disabled={isLoading || amount < 30 || !payerName.trim()}
              className="flex items-center justify-center gap-3 w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Opening...' : 'Open Payment Page'}
            </button>

            <button
              onClick={handleMoreOptions}
              disabled={isLoading || amount < 30 || !payerName.trim()}
              className="flex items-center justify-center gap-3 w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <Copy className="w-5 h-5" />
              More Options
            </button>

            {onCancel && (
              <button
                onClick={onCancel}
                disabled={isLoading}
                className="w-full bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
              >
                Cancel
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-3 pt-4">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800 font-['Poppins']">
                Choose Payment Option
              </h3>
              <p className="text-sm text-gray-600 font-['Poppins']">
                Amount: {formatAmount(amount, selectedCurrency)}
              </p>
            </div>

            <button
              onClick={handleCreatePayment}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Opening...' : 'Open in Same Tab'}
            </button>

            <button
              onClick={handlePaymentWithOptions}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Opening...' : 'Open in New Tab'}
            </button>

            <button
              onClick={() => setShowOptions(false)}
              disabled={isLoading}
              className="w-full bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
            >
              Back
            </button>
          </div>
        )}

        {/* Minimum Amount Notice */}
        <div className="text-xs text-gray-500 text-center font-['Poppins']">
          Minimum amount: €0.30
        </div>

        {/* Environment Notice */}
        {vivaConfig?.environment === 'demo' && (
          <div className="text-xs text-orange-600 text-center font-['Poppins'] bg-orange-50 p-2 rounded">
            Demo Environment - No real payments will be processed
          </div>
        )}
      </div>
    </div>
  );
}
