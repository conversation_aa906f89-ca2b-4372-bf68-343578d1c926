/**
 * Viva Wallet OAuth 2.0 Authentication Test
 * 
 * Following the official Viva Wallet OAuth 2.0 documentation:
 * Step 1: Find your client credentials ✅
 * Step 2: Request access token
 * Step 3: Receive access token  
 * Step 4: Make API calls using the access token
 * 
 * Run with: node viva-oauth2-test.js
 */

const https = require('https');
const querystring = require('querystring');

// ========================================
// Step 1: Find your client credentials
// ========================================

console.log('🔐 Viva Wallet OAuth 2.0 Authentication Test');
console.log('📚 Following official Viva documentation');
console.log('🌐 Environment: PRODUCTION');
console.log('=' .repeat(60));
console.log('');

console.log('📋 Step 1: Client Credentials Located');
console.log('─'.repeat(40));

// Smart Checkout Credentials (for OAuth 2.0)
const VIVA_CLIENT_ID = '00pp9ggt8otvtzyfy3sv7y0d5u56oleukdkd7mma293z8.apps.vivapayments.com';
const VIVA_CLIENT_SECRET = 'QMusPDEy8xF34j9r5V9yOqS7yeM2pM';

// Merchant ID & API Key (for Basic Auth operations)
const VIVA_MERCHANT_ID = '30481af3-63d9-42cd-93ea-1937a972b76d';
const VIVA_API_KEY = 'SothunZ2FxVRMkq666sbxbxB6VNbJG';

console.log('✅ Smart Checkout Credentials:');
console.log('   Client ID:', VIVA_CLIENT_ID);
console.log('   Client Secret:', VIVA_CLIENT_SECRET.substring(0, 10) + '...');
console.log('');
console.log('✅ Merchant Credentials:');
console.log('   Merchant ID:', VIVA_MERCHANT_ID);
console.log('   API Key:', VIVA_API_KEY.substring(0, 10) + '...');
console.log('');

// Production environment endpoints
const OAUTH_ENDPOINT = 'accounts.vivapayments.com';
const API_ENDPOINT = 'api.vivapayments.com';

// ========================================
// Step 2: Request access token
// ========================================

/**
 * Method 1: Base64-encoded format (as per documentation)
 */
async function requestAccessTokenBase64() {
  return new Promise((resolve) => {
    console.log('📋 Step 2: Request Access Token (Base64 Method)');
    console.log('─'.repeat(40));
    console.log('🌐 Endpoint: https://accounts.vivapayments.com/connect/token');
    console.log('🔒 Method: Base64-encoded credentials in Authorization header');
    console.log('');

    // Base64-encode credentials in format Client_ID:Client_Secret
    const credentials = `${VIVA_CLIENT_ID}:${VIVA_CLIENT_SECRET}`;
    const base64Credentials = Buffer.from(credentials).toString('base64');
    
    console.log('🔑 Credentials Format: Client_ID:Client_Secret');
    console.log('🔑 Raw Credentials Length:', credentials.length);
    console.log('🔑 Base64 Encoded:', base64Credentials.substring(0, 50) + '...');
    console.log('');

    // Prepare request data
    const postData = querystring.stringify({
      grant_type: 'client_credentials'
    });

    const options = {
      hostname: OAUTH_ENDPOINT,
      port: 443,
      path: '/connect/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${base64Credentials}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('📤 Request Configuration:');
    console.log('   Method: POST');
    console.log('   URL: https://' + options.hostname + options.path);
    console.log('   Content-Type: application/x-www-form-urlencoded');
    console.log('   Authorization: Basic [base64_credentials]');
    console.log('   Body: grant_type=client_credentials');
    console.log('');

    console.log('🚀 Sending OAuth 2.0 token request...');
    console.log('');

    const req = https.request(options, (res) => {
      console.log('📥 Response Received:');
      console.log('   Status Code:', res.statusCode);
      console.log('   Status Message:', res.statusMessage);
      console.log('   Headers:', JSON.stringify(res.headers, null, 4));
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Response Body:', data || '(empty)');
        console.log('');

        // ========================================
        // Step 3: Receive access token
        // ========================================
        console.log('📋 Step 3: Process Access Token Response');
        console.log('─'.repeat(40));

        try {
          if (res.statusCode === 200) {
            const tokenResponse = JSON.parse(data);
            
            console.log('🎉 SUCCESS! OAuth 2.0 Authentication Successful');
            console.log('✅ Access Token Received:');
            console.log('   Token:', tokenResponse.access_token.substring(0, 50) + '...');
            console.log('   Type:', tokenResponse.token_type);
            console.log('   Expires In:', tokenResponse.expires_in, 'seconds');
            console.log('   Scope:', tokenResponse.scope);
            console.log('');

            resolve({
              success: true,
              accessToken: tokenResponse.access_token,
              tokenType: tokenResponse.token_type,
              expiresIn: tokenResponse.expires_in,
              scope: tokenResponse.scope
            });
          } else {
            const errorResponse = data ? JSON.parse(data) : {};
            
            console.log('❌ FAILED! OAuth 2.0 Authentication Failed');
            console.log('❌ Status:', res.statusCode, res.statusMessage);
            console.log('❌ Error:', errorResponse.error || 'Unknown error');
            console.log('❌ Description:', errorResponse.error_description || 'No description');
            console.log('');

            if (errorResponse.error === 'invalid_client') {
              console.log('🔧 TROUBLESHOOTING: Invalid Client Error');
              console.log('   1. Verify Client ID is correct in Viva dashboard');
              console.log('   2. Verify Client Secret is correct and not expired');
              console.log('   3. Ensure Smart Checkout is enabled');
              console.log('   4. Check you\'re using demo environment credentials');
              console.log('   5. Try regenerating the Client Secret');
              console.log('');
            }

            resolve({
              success: false,
              error: errorResponse.error,
              description: errorResponse.error_description,
              statusCode: res.statusCode
            });
          }
        } catch (parseError) {
          console.log('❌ FAILED! Could not parse response as JSON');
          console.log('❌ Raw response:', data);
          console.log('❌ Parse error:', parseError.message);
          console.log('');

          resolve({
            success: false,
            error: 'parse_error',
            description: 'Could not parse response',
            rawResponse: data
          });
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ NETWORK ERROR:', error.message);
      console.log('');
      resolve({
        success: false,
        error: 'network_error',
        description: error.message
      });
    });

    req.write(postData);
    req.end();
  });
}

// ========================================
// Step 4: Make API calls using the access token
// ========================================

async function testPaymentCreationWithToken(tokenData) {
  return new Promise((resolve) => {
    console.log('📋 Step 4: Make API Calls Using Access Token');
    console.log('─'.repeat(40));
    console.log('🎯 Testing: Payment Order Creation');
    console.log('🌐 Endpoint: https://api.vivapayments.com/checkout/v2/orders');
    console.log('');

    const paymentData = {
      amount: 100, // €1.00 in cents
      customerTrns: 'OAuth 2.0 Test Payment',
      customer: {
        email: '<EMAIL>',
        fullName: 'OAuth Test Customer',
        requestLang: 'en-GB'
      },
      sourceCode: 'Default',
      merchantTrns: 'OAUTH_TEST_' + Date.now(),
      currencyCode: '978', // EUR (more commonly supported)
      paymentTimeout: 1800
    };

    const postData = JSON.stringify(paymentData);

    const options = {
      hostname: API_ENDPOINT,
      port: 443,
      path: '/checkout/v2/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${tokenData.tokenType} ${tokenData.accessToken}`,
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('📤 Payment Request:');
    console.log('   Authorization: Bearer [access_token]');
    console.log('   Data:', JSON.stringify(paymentData, null, 4));
    console.log('');

    console.log('🚀 Creating payment order...');
    console.log('');

    const req = https.request(options, (res) => {
      console.log('📥 Payment Response:');
      console.log('   Status:', res.statusCode, res.statusMessage);
      console.log('');

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('📥 Response Body:', data || '(empty)');
        console.log('');

        if (res.statusCode === 200 || res.statusCode === 201) {
          try {
            const paymentResponse = JSON.parse(data);
            
            console.log('🎉 SUCCESS! Payment Order Created');
            console.log('✅ Order Code:', paymentResponse.orderCode || paymentResponse.OrderCode);
            console.log('✅ Checkout URL: https://www.vivapayments.com/web/checkout?ref=' + (paymentResponse.orderCode || paymentResponse.OrderCode));
            console.log('');
            
            resolve({ success: true, orderCode: paymentResponse.OrderCode });
          } catch (e) {
            console.log('✅ Payment created but response not JSON');
            resolve({ success: true });
          }
        } else {
          console.log('❌ Payment creation failed');
          try {
            const errorResponse = JSON.parse(data);
            console.log('❌ Error:', errorResponse);
          } catch (e) {
            console.log('❌ Non-JSON error response');
          }
          resolve({ success: false });
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Payment request error:', error.message);
      resolve({ success: false });
    });

    req.write(postData);
    req.end();
  });
}

// ========================================
// Main Execution
// ========================================

async function main() {
  console.log('🚀 Starting OAuth 2.0 Authentication Flow');
  console.log('');

  // Step 2 & 3: Request and receive access token
  const tokenResult = await requestAccessTokenBase64();

  if (tokenResult.success) {
    // Step 4: Use access token for API calls
    await testPaymentCreationWithToken(tokenResult);
  }

  console.log('📋 FINAL SUMMARY');
  console.log('=' .repeat(60));
  
  if (tokenResult.success) {
    console.log('🎉 OAuth 2.0 Authentication: SUCCESS');
    console.log('✅ Access token obtained successfully');
    console.log('✅ Ready for payment creation');
    console.log('✅ Integration is fully functional');
  } else {
    console.log('❌ OAuth 2.0 Authentication: FAILED');
    console.log('❌ Error:', tokenResult.error);
    console.log('❌ Description:', tokenResult.description);
    console.log('');
    console.log('🔧 Next Steps:');
    console.log('1. Check Client ID and Client Secret in Viva dashboard');
    console.log('2. Ensure Smart Checkout is enabled');
    console.log('3. Try regenerating credentials if needed');
    console.log('4. Contact Viva support if issue persists');
  }
  
  console.log('');
  console.log('📞 Support: https://developer.viva.com/');
  console.log('🌐 Dashboard: https://www.vivapayments.com/');
  console.log('=' .repeat(60));
}

// Run the OAuth 2.0 test
main();
