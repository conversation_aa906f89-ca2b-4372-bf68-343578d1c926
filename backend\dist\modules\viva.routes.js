"use strict";
/**
 * Viva Wallet Payment Routes
 *
 * Handles Viva Wallet payment processing endpoints
 * Provides API for creating payments, checking payment status, and configuration
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = vivaRoutes;
const vivaPaymentService_1 = require("../services/vivaPaymentService");
const logger_1 = require("../config/logger");
const env_1 = require("../config/env");
const Transaction_mongo_1 = __importDefault(require("../models/Transaction.mongo"));
const zod_1 = require("zod");
const vivaLogger = logger_1.logger.child({ module: 'viva-routes' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int().min(30), // Minimum 30 pence as per Viva requirements
    currency: zod_1.z.string().length(3).default('EUR'),
    customerTrns: zod_1.z.string().optional(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    sourceCode: zod_1.z.string().optional(),
    type: zod_1.z.enum(['url']).optional().default('url'), // Viva only supports payment URLs
});
const orderCodeParamsSchema = zod_1.z.object({
    orderCode: zod_1.z.string().transform(val => parseInt(val, 10)).pipe(zod_1.z.number().positive()),
});
// Helper function to map Viva status to our transaction status
function mapVivaStatusToTransactionStatus(vivaStatus) {
    switch (vivaStatus) {
        case 'completed':
            return 'success';
        case 'pending':
            return 'pending';
        case 'failed':
            return 'failed';
        case 'expired':
            return 'failed';
        case 'cancelled':
            return 'cancelled';
        default:
            return 'failed';
    }
}
async function vivaRoutes(fastify) {
    /**
     * Create Viva Wallet payment
     */
    fastify.post('/viva/payment', async (request, reply) => {
        try {
            console.log('=== RAW VIVA REQUEST BODY ===', JSON.stringify(request.body, null, 2));
            const validatedBody = createPaymentSchema.parse(request.body);
            vivaLogger.info('Creating Viva Wallet payment', {
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                payerName: validatedBody.payerName,
            });
            // Check for existing payment (idempotency)
            const existingTransaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_id': validatedBody.orderId,
                'metadata.payment_provider': 'viva',
            });
            if (existingTransaction) {
                vivaLogger.info('Returning existing Viva Wallet payment', {
                    orderId: validatedBody.orderId,
                    transactionId: existingTransaction._id,
                });
                return reply.send({
                    success: true,
                    data: {
                        transaction_id: existingTransaction._id,
                        order_id: validatedBody.orderId,
                        orderCode: existingTransaction.metadata?.order_code,
                        redirect_url: existingTransaction.metadata?.redirect_url,
                        amount: validatedBody.amount,
                        currency: validatedBody.currency,
                        type: validatedBody.type || 'url',
                        status: existingTransaction.status,
                    },
                });
            }
            // Create payment with Viva Wallet
            const vivaResponse = await vivaPaymentService_1.vivaPaymentService.createPayment({
                orderId: validatedBody.orderId,
                amount: validatedBody.amount,
                currency: validatedBody.currency,
                customerTrns: validatedBody.customerTrns,
                payerName: validatedBody.payerName,
                payerEmail: validatedBody.payerEmail,
                successUrl: validatedBody.successUrl,
                cancelUrl: validatedBody.cancelUrl,
                sourceCode: validatedBody.sourceCode,
            });
            if (!vivaResponse.success || !vivaResponse.data) {
                vivaLogger.error('Viva Wallet payment creation failed', {
                    orderId: validatedBody.orderId,
                    error: vivaResponse.error,
                    message: vivaResponse.message,
                });
                // Provide helpful error message for OAuth2 issues
                let errorMessage = vivaResponse.message || 'Failed to create payment';
                let errorDetails = vivaResponse.details;
                if (vivaResponse.message?.includes('OAuth2 Authentication Failed')) {
                    errorMessage = 'Viva Wallet OAuth2 configuration issue. Please check your Smart Checkout credentials in the Viva dashboard.';
                    errorDetails = {
                        ...vivaResponse.details,
                        helpUrl: 'https://demo.vivapayments.com/',
                        troubleshooting: [
                            'Verify Client ID and Client Secret in Viva dashboard',
                            'Ensure Smart Checkout is enabled',
                            'Check that credentials are for demo environment',
                            'Contact Viva support if needed'
                        ]
                    };
                }
                return reply.status(400).send({
                    success: false,
                    error: vivaResponse.error || 'PAYMENT_CREATION_FAILED',
                    message: errorMessage,
                    details: errorDetails,
                });
            }
            // Create transaction record in database
            let transaction = null;
            try {
                vivaLogger.info('Creating database transaction record', {
                    vivaResponseData: vivaResponse.data,
                    orderCode: vivaResponse.data.orderCode,
                    orderCodeType: typeof vivaResponse.data.orderCode,
                });
                transaction = new Transaction_mongo_1.default({
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    status: 'pending',
                    paymentMethod: 'viva_wallet',
                    paymentProvider: 'viva',
                    metadata: {
                        payment_provider: 'viva',
                        order_id: validatedBody.orderId,
                        order_code: String(vivaResponse.data.orderCode), // Convert to string for safety
                        redirect_url: vivaResponse.data.checkout_url,
                        payer_name: validatedBody.payerName,
                        payer_email: validatedBody.payerEmail,
                        customer_trns: validatedBody.customerTrns,
                        source_code: validatedBody.sourceCode,
                        environment: env_1.env.VIVA_ENVIRONMENT,
                    },
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                });
                vivaLogger.info('Saving transaction to database', {
                    transactionData: transaction.toObject(),
                });
                await transaction.save();
                vivaLogger.info('Transaction saved successfully', {
                    transactionId: transaction._id,
                });
            }
            catch (dbError) {
                vivaLogger.error('Failed to save transaction to database', {
                    error: dbError.message,
                    stack: dbError.stack,
                    vivaResponseData: vivaResponse.data,
                });
                // Even if DB save fails, we should still return the payment response
                // since the payment was created successfully in Viva
                vivaLogger.warn('Continuing despite database error - payment was created in Viva');
            }
            vivaLogger.info('Viva Wallet payment created successfully', {
                orderId: validatedBody.orderId,
                transactionId: transaction?._id,
                orderCode: vivaResponse.data.orderCode,
                checkoutUrl: vivaResponse.data.checkout_url,
            });
            return reply.send({
                success: true,
                data: {
                    transaction_id: transaction?._id || null,
                    order_id: validatedBody.orderId,
                    orderCode: vivaResponse.data.orderCode,
                    redirect_url: vivaResponse.data.checkout_url,
                    amount: validatedBody.amount,
                    currency: validatedBody.currency,
                    type: validatedBody.type || 'url',
                    status: vivaResponse.data.status,
                },
            });
        }
        catch (error) {
            vivaLogger.error('Viva Wallet payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request data',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Viva Wallet payment status by order code
     */
    fastify.get('/viva/payment/:orderCode/status', async (request, reply) => {
        try {
            const { orderCode } = orderCodeParamsSchema.parse(request.params);
            vivaLogger.info('Getting Viva Wallet payment status', { orderCode });
            // Get payment status from Viva Wallet
            const vivaResponse = await vivaPaymentService_1.vivaPaymentService.getPaymentStatus(orderCode);
            if (!vivaResponse.success || !vivaResponse.data) {
                vivaLogger.warn('Viva Wallet payment status retrieval failed', {
                    orderCode,
                    error: vivaResponse.error,
                    message: vivaResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: vivaResponse.error || 'PAYMENT_STATUS_RETRIEVAL_FAILED',
                    message: vivaResponse.message || 'Failed to get payment status',
                    details: vivaResponse.details,
                });
            }
            // Update transaction status in database if it exists
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_code': orderCode,
                'metadata.payment_provider': 'viva',
            });
            if (transaction) {
                // Map Viva status to our transaction status
                const newStatus = mapVivaStatusToTransactionStatus(vivaResponse.data.status);
                if (transaction.status !== newStatus) {
                    const oldStatus = transaction.status;
                    transaction.status = newStatus;
                    transaction.updatedAt = new Date().toISOString();
                    await transaction.save();
                    vivaLogger.info('Updated transaction status', {
                        transactionId: transaction._id,
                        orderCode,
                        oldStatus,
                        newStatus,
                    });
                }
            }
            vivaLogger.info('Viva Wallet payment status retrieved successfully', {
                orderCode,
                status: vivaResponse.data.status,
                stateId: vivaResponse.data.stateId,
            });
            return reply.send({
                success: true,
                data: {
                    orderCode: vivaResponse.data.orderCode,
                    status: vivaResponse.data.status,
                    amount: vivaResponse.data.amount,
                    currency: vivaResponse.data.currency,
                    stateId: vivaResponse.data.stateId,
                    expirationDate: vivaResponse.data.expirationDate,
                    merchantTrns: vivaResponse.data.merchantTrns,
                    customerTrns: vivaResponse.data.customerTrns,
                },
            });
        }
        catch (error) {
            vivaLogger.error('Viva Wallet payment status retrieval failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Cancel Viva Wallet payment by order code
     */
    fastify.delete('/viva/payment/:orderCode', async (request, reply) => {
        try {
            const { orderCode } = orderCodeParamsSchema.parse(request.params);
            vivaLogger.info('Cancelling Viva Wallet payment', { orderCode });
            // Cancel payment with Viva Wallet
            const vivaResponse = await vivaPaymentService_1.vivaPaymentService.cancelPayment(orderCode);
            if (!vivaResponse.success || !vivaResponse.data) {
                vivaLogger.warn('Viva Wallet payment cancellation failed', {
                    orderCode,
                    error: vivaResponse.error,
                    message: vivaResponse.message,
                });
                return reply.status(400).send({
                    success: false,
                    error: vivaResponse.error || 'PAYMENT_CANCELLATION_FAILED',
                    message: vivaResponse.message || 'Failed to cancel payment',
                    details: vivaResponse.details,
                });
            }
            // Update transaction status in database if it exists
            const transaction = await Transaction_mongo_1.default.findOne({
                'metadata.order_code': orderCode,
                'metadata.payment_provider': 'viva',
            });
            if (transaction) {
                transaction.status = 'cancelled';
                transaction.updatedAt = new Date().toISOString();
                await transaction.save();
                vivaLogger.info('Updated transaction status to cancelled', {
                    transactionId: transaction._id,
                    orderCode,
                });
            }
            vivaLogger.info('Viva Wallet payment cancelled successfully', { orderCode });
            return reply.send({
                success: true,
                data: {
                    orderCode: vivaResponse.data.orderCode,
                    cancelled: vivaResponse.data.cancelled,
                },
            });
        }
        catch (error) {
            vivaLogger.error('Viva Wallet payment cancellation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return reply.status(400).send({
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid request parameters',
                    details: error.errors,
                });
            }
            return reply.status(500).send({
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
            });
        }
    });
    /**
     * Get Viva Wallet configuration
     */
    fastify.get('/viva/config', async (_request, reply) => {
        try {
            vivaLogger.info('Fetching Viva Wallet configuration');
            // Get the correct checkout URL based on environment
            const checkoutUrl = env_1.env.VIVA_ENVIRONMENT === 'production'
                ? 'https://www.vivapayments.com'
                : 'https://demo.vivapayments.com';
            return reply.send({
                success: true,
                data: {
                    environment: env_1.env.VIVA_ENVIRONMENT,
                    enabled: env_1.env.VIVA_ENABLED,
                    currency: 'EUR', // Default currency for Viva Wallet (EUR is supported)
                    sourceCode: env_1.env.VIVA_SOURCE_CODE,
                    checkoutUrl,
                    minAmount: 30, // Minimum amount in cents (30 cents)
                    maxAmount: 99999999, // Maximum amount in pence
                },
            });
        }
        catch (error) {
            vivaLogger.error('Failed to fetch Viva Wallet configuration', error);
            return reply.status(500).send({
                success: false,
                error: 'CONFIG_FETCH_FAILED',
                message: 'Failed to fetch configuration',
            });
        }
    });
    /**
     * Viva Wallet health check
     */
    fastify.get('/viva/health', async (_request, reply) => {
        try {
            vivaLogger.info('Performing Viva Wallet health check');
            const healthResponse = await vivaPaymentService_1.vivaPaymentService.healthCheck();
            if (!healthResponse.success) {
                vivaLogger.warn('Viva Wallet health check failed', {
                    error: healthResponse.error,
                    message: healthResponse.message,
                });
                return reply.status(503).send({
                    success: false,
                    error: healthResponse.error || 'HEALTH_CHECK_FAILED',
                    message: healthResponse.message || 'Service unavailable',
                    details: healthResponse.details,
                });
            }
            vivaLogger.info('Viva Wallet health check passed');
            return reply.send({
                success: true,
                data: {
                    status: 'healthy',
                    environment: healthResponse.data?.environment,
                    timestamp: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            vivaLogger.error('Viva Wallet health check failed', error);
            return reply.status(503).send({
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Service unavailable',
            });
        }
    });
}
//# sourceMappingURL=viva.routes.js.map